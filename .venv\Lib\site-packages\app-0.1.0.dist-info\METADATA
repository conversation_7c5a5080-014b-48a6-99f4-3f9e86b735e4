Metadata-Version: 2.4
Name: app
Version: 0.1.0
Author-email: <PERSON> <mail@marc<PERSON><PERSON>esser.de>
Requires-Python: <3.14,>=3.11
Requires-Dist: aiostream<0.6.0,>=0.5.2
Requires-Dist: chromadb>=0.6.3
Requires-Dist: docx2txt<0.9,>=0.8
Requires-Dist: llama-index-agent-openai<0.5.0,>=0.4.0
Requires-Dist: llama-index-core<0.13.0,>=0.12.28
Requires-Dist: llama-index-embeddings-openai<0.4.0,>=0.3.1
Requires-Dist: llama-index-llms-openai<0.4.0,>=0.3.2
Requires-Dist: llama-index-server<0.2.0,>=0.1.17
Requires-Dist: llama-index-vector-stores-chroma>=0.4.2
Requires-Dist: pydantic<2.10
Requires-Dist: python-dotenv<2.0.0,>=1.0.0
Requires-Dist: sse-starlette>=2.3.6
Provides-Extra: dev
Requires-Dist: mypy<2.0.0,>=1.8.0; extra == 'dev'
Requires-Dist: pytest-asyncio<0.26.0,>=0.25.3; extra == 'dev'
Requires-Dist: pytest<9.0.0,>=8.3.5; extra == 'dev'
Description-Content-Type: text/markdown

This is a [LlamaIndex](https://www.llamaindex.ai/) simple agentic RAG project using [Agent Workflows](https://docs.llamaindex.ai/en/stable/examples/agent/agent_workflow_basic/).

## Getting Started

First, setup the environment with uv:

> **_Note:_** This step is not needed if you are using the dev-container.

```shell
uv sync
```

Then check the parameters that have been pre-configured in the `.env` file in this directory.
Make sure you have set the `OPENAI_API_KEY` for the LLM.

Second, generate the embeddings of the documents in the `./data` directory:

```shell
uv run generate
```

Third, run the development server:

```shell
uv run fastapi dev
```

Then open [http://localhost:8000](http://localhost:8000) with your browser to start the chat UI.

To start the app optimized for **production**, run:

```
uv run fastapi run
```

## Configure LLM and Embedding Model

You can configure [LLM model](https://docs.llamaindex.ai/en/stable/module_guides/models/llms) and [embedding model](https://docs.llamaindex.ai/en/stable/module_guides/models/embeddings) in [settings.py](app/settings.py).

## Use Case

We have prepared an [example workflow](./app/workflow.py) for the agentic RAG use case, where you can ask questions about the example documents in the [./data](./data) directory.

You can start by sending an request on the [chat UI](http://localhost:8000) or you can test the `/api/chat` endpoint with the following curl request:

```
curl --location 'localhost:8000/api/chat' \
--header 'Content-Type: application/json' \
--data '{ "messages": [{ "role": "user", "content": "What standards for a letter exist?" }] }'
```

## Learn More

To learn more about LlamaIndex, take a look at the following resources:

- [LlamaIndex Documentation](https://docs.llamaindex.ai) - learn about LlamaIndex.
- [Workflows Introduction](https://docs.llamaindex.ai/en/stable/understanding/workflows/) - learn about LlamaIndex workflows.

You can check out [the LlamaIndex GitHub repository](https://github.com/run-llama/llama_index) - your feedback and contributions are welcome!
